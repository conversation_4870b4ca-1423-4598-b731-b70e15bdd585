import { Annotation } from "@langchain/langgraph";
import { StateGraph, END } from "@langchain/langgraph";
import { AIMessage } from "@langchain/core/messages";
import { ChatOpenAI } from "@langchain/openai";
import { ToolNode } from "@langchain/langgraph/prebuilt";
import { coreTools } from "./tools/index.js";
import type { BaseMessageLike } from "@langchain/core/messages";
import { isAIMessageChunk } from "@langchain/core/messages";
import { DEFAULT_OPENAI_CONFIGS } from "./config/models.js";

// 1. Define the state
const StateAnnotation = Annotation.Root({
  messages: Annotation<BaseMessageLike[]>({
    reducer: (x, y) => x.concat(y),
  }),
});

// 2. Set up the tools - using core tools from packages/core implementation
const toolNode = new ToolNode(coreTools);

// 3. Set up the model
const model = new ChatOpenAI(DEFAULT_OPENAI_CONFIGS);
const boundModel = model.bindTools(coreTools);

// 4. Define the graph
const routeMessage = (state: typeof StateAnnotation.State) => {
  const { messages } = state;
  const lastMessage = messages[messages.length - 1] as AIMessage;
  // If no tools are called, we can finish (respond to the user)
  if (!lastMessage?.tool_calls?.length) {
    return END;
  }
  // Otherwise if there is, we continue and call the tools
  return "tools";
};

const callModel = async (
  state: typeof StateAnnotation.State,
) => {
  // For versions of @langchain/core < 0.2.3, you must call `.stream()`
  // and aggregate the message from chunks instead of calling `.invoke()`.
  const { messages } = state;
  const responseMessage = await boundModel.invoke(messages);
  return { messages: [responseMessage] };
};

const workflow = new StateGraph(StateAnnotation)
  .addNode("agent", callModel)
  .addNode("tools", toolNode)
  .addEdge("__start__", "agent")
  .addConditionalEdges("agent", routeMessage)
  .addEdge("tools", "agent");

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const agent = workflow.compile();

// 5. Streaming LLM Tokens
const stream = await agent.stream(
  { messages: [{ role: "user", content: "Please read the package.json file and tell me about this project." }] },
  { streamMode: "messages" },
);

for await (const [message, _metadata] of stream) {
  if (isAIMessageChunk(message) && message.tool_call_chunks?.length) {
    console.log(`${message.getType()} MESSAGE TOOL CALL CHUNK: ${message.tool_call_chunks[0].args}`);
  } else {
    console.log(`${message.getType()} MESSAGE CONTENT: ${message.content}`);
  }
}

// const StateAnnotation = Annotation.Root({
//   ...MessagesAnnotation.spec,
//   // user provided
//   lastName: Annotation<string>,
//   // updated by the tool
//   userInfo: Annotation<Record<string, any>>,
// });

// const stateModifier = (state: typeof StateAnnotation.State) => {
//   const userInfo = state.userInfo;
//   if (userInfo == null) {
//     return state.messages;
//   }
//   return [{
//     role: "system",
//     content: BASE_PROMPT,
//   }, ...state.messages];
// };

// const model = new ChatOpenAI(DEFAULT_OPENAI_CONFIGS);

// const agent = createReactAgent({
//   llm: model,
//   tools: [lookupUserInfo],
//   stateSchema: StateAnnotation,
//   // stateModifier: stateModifier as any,
// })

// const stream = await agent.stream({
//   messages: [{
//     role: "user",
//     content: "hi, what should i do this weekend?",
//   }],

// }, {
//   // provide user ID in the config
//   configurable: { user_id: getUserName() }
// });

// for await (const chunk of stream) {
//   console.log(chunk);
// }