/**
 * Tool registry for langchain implementation
 * Exports all implemented tools for easy import and usage
 */

// Import all tools
import { readFileTool } from './readFile.js';
import { writeFileTool } from './writeFile.js';
import { shellTool } from './shell.js';
import { webSearchTool } from './webSearch.js';
import { grepTool } from './grep.js';
import { globTool } from './glob.js';
import { editTool } from './edit.js';
import { memoryTool } from './memoryTool.js';

// Export individual tools
export {
  readFileTool,
  writeFileTool,
  shellTool,
  webSearchTool,
  grepTool,
  globTool,
  editTool,
  memoryTool,
};

// Export all tools as an array for easy registration
export const allTools = [
  readFileTool,
  writeFileTool,
  shellTool,
  webSearchTool,
  grepTool,
  globTool,
  editTool,
  memoryTool,
];

// Export core tools
export const coreTools = [
  readFileTool,
  writeFileTool,
  shellTool,
  // webSearchTool,
  grepTool,
  globTool,
  editTool,
  memoryTool,
];

// Export file operation tools
export const fileTools = [
  readFileTool,
  writeFileTool,
  editTool,
  grepTool,
  globTool,
];

// Export system tools
export const systemTools = [
  shellTool,
  webSearchTool,
  memoryTool,
];
